'use client';

import Image from 'next/image';
import { useState, useRef, useEffect } from 'react';

/**
 * BAKASANA Enhanced Optimized Image Component
 * High-performance image component for Lighthouse >95 scores
 */

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  quality = 85,
  placeholder = 'blur',
  blurDataURL,
  className = '',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  fill = false,
  loading = 'lazy',
  onLoad,
  ...props
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef(null);

  // Generate blur placeholder if not provided
  const defaultBlurDataURL = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || !imgRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before entering viewport
        threshold: 0.1
      }
    );

    observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, [priority]);

  const handleLoad = (event) => {
    setIsLoading(false);
    if (onLoad) onLoad(event);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  // Generate responsive srcSet for WebP/AVIF
  const generateModernSrcSet = (baseSrc) => {
    const sizes = [480, 640, 750, 828, 1080, 1200, 1920];
    const ext = baseSrc.split('.').pop();
    const baseName = baseSrc.replace(`.${ext}`, '');
    
    return sizes
      .map(size => `${baseName}-${size}.webp ${size}w`)
      .join(', ');
  };

  // Error fallback component
  if (hasError) {
    return (
      <div 
        className={`bg-enterprise-brown/5 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <div className="text-center text-enterprise-brown/60">
          <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
          <p className="text-xs">Nie można załadować obrazu</p>
        </div>
      </div>
    );
  }

  const imageProps = {
    src,
    alt,
    quality,
    onLoad: handleLoad,
    onError: handleError,
    className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,
    placeholder: placeholder === 'blur' ? 'blur' : 'empty',
    blurDataURL: blurDataURL || defaultBlurDataURL,
    loading: priority ? 'eager' : loading,
    priority,
    sizes,
    ...props
  };

  if (fill) {
    return (
      <div className="relative overflow-hidden">
        <Image
          {...imageProps}
          fill
          style={{ objectFit: 'cover' }}
        />
        {isLoading && (
          <div className="absolute inset-0 bg-enterprise-brown/5 animate-pulse" />
        )}
      </div>
    );
  }

  return (
    <div ref={imgRef} className="relative">
      {/* Only load image when in view (unless priority) */}
      {isInView && (
        <picture>
          {/* AVIF format for maximum compression */}
          <source
            srcSet={generateModernSrcSet(src).replace(/\.webp/g, '.avif')}
            sizes={sizes}
            type="image/avif"
          />
          
          {/* WebP format for good compression */}
          <source
            srcSet={generateModernSrcSet(src)}
            sizes={sizes}
            type="image/webp"
          />
          
          {/* Fallback to Next.js Image */}
          <Image
            {...imageProps}
            width={width}
            height={height}
          />
        </picture>
      )}
      
      {/* Loading placeholder */}
      {(isLoading || !isInView) && (
        <div 
          className="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center"
          style={{ width, height }}
        >
          <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
}

// Specialized components for common use cases
export function HeroImage({ src, alt, className = '', priority = true }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      fill
      priority={priority}
      quality={90}
      sizes="100vw"
      className={className}
      placeholder="blur"
    />
  );
}

export function GalleryImage({ src, alt, className = '' }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={400}
      height={300}
      quality={85}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      className={`rectangular ${className}`}
      placeholder="blur"
    />
  );
}

export function ProfileImage({ src, alt, size = 200, className = '' }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      quality={90}
      sizes={`${size}px`}
      className={`rectangular ${className}`}
      placeholder="blur"
    />
  );
}

export function BlogImage({ src, alt, className = '' }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={800}
      height={450}
      quality={85}
      sizes="(max-width: 768px) 100vw, 800px"
      className={`rectangular ${className}`}
      placeholder="blur"
    />
  );
}

export function ThumbnailImage({ src, alt, className = '' }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={150}
      height={150}
      quality={80}
      sizes="150px"
      className={`rectangular ${className}`}
      placeholder="blur"
    />
  );
}

// Lazy loading image with intersection observer
export function LazyImage({ 
  src, 
  alt, 
  width, 
  height, 
  className = '',
  threshold = 0.1,
  rootMargin = '50px'
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [imgRef, setImgRef] = useState(null);

  useState(() => {
    if (!imgRef || !('IntersectionObserver' in window)) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    observer.observe(imgRef);

    return () => observer.disconnect();
  }, [imgRef, threshold, rootMargin]);

  return (
    <div 
      ref={setImgRef}
      className={`bg-enterprise-brown/5 ${className}`}
      style={{ width, height }}
    >
      {isVisible ? (
        <OptimizedImage
          src={src}
          alt={alt}
          width={width}
          height={height}
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="w-full h-full bg-enterprise-brown/5 animate-pulse flex items-center justify-center">
          <svg className="w-8 h-8 text-enterprise-brown/30" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </div>
  );
}

// Progressive image loading with multiple sizes
export function ProgressiveImage({ 
  src, 
  alt, 
  lowQualitySrc,
  className = '',
  ...props 
}) {
  const [isHighQualityLoaded, setIsHighQualityLoaded] = useState(false);

  return (
    <div className="relative">
      {/* Low quality placeholder */}
      {lowQualitySrc && !isHighQualityLoaded && (
        <OptimizedImage
          src={lowQualitySrc}
          alt={alt}
          quality={20}
          className={`absolute inset-0 ${className}`}
          {...props}
        />
      )}
      
      {/* High quality image */}
      <OptimizedImage
        src={src}
        alt={alt}
        onLoad={() => setIsHighQualityLoaded(true)}
        className={`${className} ${isHighQualityLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500`}
        {...props}
      />
    </div>
  );
}
