// src/app/admin/page.jsx
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import UnifiedButton from '@/components/ui/UnifiedButton';
import { SectionTitle, BodyText, CardTitle } from '@/components/ui/UnifiedTypography';

export default function AdminPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Sprawdź czy użytkownik jest już zalogowany
    const token = localStorage.getItem('admin-token');
    if (token) {
      verifyToken(token);
    } else {
      setLoading(false);
    }
  }, []);

  const verifyToken = async (token) => {
    try {
      const response = await fetch('/api/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setIsAuthenticated(true);
      } else {
        localStorage.removeItem('admin-token');
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      localStorage.removeItem('admin-token');
    }
    setLoading(false);
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');

    try {
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('admin-token', data.token);
        setIsAuthenticated(true);
      } else {
        setError(data.error || 'Nieprawidłowe hasło');
      }
    } catch (error) {
      setError('Błąd połączenia');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin-token');
    setIsAuthenticated(false);
    setPassword('');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-b-2 border-charcoal mx-auto mb-sm"></div>
          <p className="text-charcoal">Ładowanie...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-sanctuary to-whisper">
        <div className="bg-white p-8 shadow-soft max-w-md w-full mx-4">
          <div className="text-center mb-lg">
            <SectionTitle className="mb-2">Panel Administracyjny</SectionTitle>
            <BodyText className="text-charcoal-light">Bakasana Travel Blog</BodyText>
          </div>

          <form onSubmit={handleLogin} className="space-y-md">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-charcoal mb-2">
                Hasło administratora
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-container-sm py-3 border border-charcoal/20 focus:ring-2 focus:ring-enterprise-brown/20 focus:border-charcoal transition-colors"
                placeholder="Wprowadź hasło"
                required
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-container-sm py-3 text-sm">
                {error}
              </div>
            )}

            <button
              type="submit"
              className="w-full bg-charcoal text-white py-3 px-container-sm hover:bg-charcoal/90 transition-colors font-medium"
            >
              Zaloguj się
            </button>
          </form>

          <div className="mt-md text-center">
            <p className="text-xs text-charcoal-light">
              🔒 Zabezpieczone połączenie
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-sanctuary to-whisper">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-charcoal/10">
        <div className="max-w-7xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <CardTitle>Panel Administracyjny</CardTitle>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-charcoal-light">Zalogowany jako Administrator</span>
              <button
                onClick={handleLogout}
                className="text-sm text-charcoal hover:text-charcoal/70 transition-colors"
              >
                Wyloguj się
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 px-container-sm sm:px-hero-padding lg:px-hero-padding">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-md">
          
          {/* Sanity CMS */}
          <div className="bg-white shadow-soft p-6">
            <div className="flex items-center mb-sm">
              <div className="w-12 h-12 bg-charcoal/10 flex items-center justify-center mr-4">
                <span className="text-2xl /* TODO: Replace with SectionTitle */">📝</span>
              </div>
              <div>
                <CardTitle level={4}>Sanity CMS</CardTitle>
                <p className="text-sm text-charcoal-light">Zarządzaj treścią</p>
              </div>
            </div>
            <p className="text-charcoal-light text-sm mb-sm">
              Edytuj retreaty, opinie, FAQ i artykuły bloga
            </p>
            <a
              href={process.env.NEXT_PUBLIC_SANITY_STUDIO_URL || 'https://bakasana-travel.sanity.studio'}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-container-sm py-2 bg-charcoal text-white text-sm hover:bg-charcoal/90 transition-colors"
            >
              Otwórz CMS
              <span className="ml-2">→</span>
            </a>
          </div>

          {/* Analytics */}
          <div className="bg-white shadow-soft p-6">
            <div className="flex items-center mb-sm">
              <div className="w-12 h-12 bg-terra/10 flex items-center justify-center mr-4">
                <span className="text-2xl">📊</span>
              </div>
              <div>
                <CardTitle level={4}>Analytics</CardTitle>
                <p className="text-sm text-charcoal-light">Statystyki strony</p>
              </div>
            </div>
            <p className="text-charcoal-light text-sm mb-sm">
              Zobacz raporty odwiedzin i konwersji
            </p>
            <a
              href="https://analytics.google.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-container-sm py-2 bg-terra text-white text-sm hover:bg-terra/90 transition-colors"
            >
              Google Analytics
              <span className="ml-2">→</span>
            </a>
          </div>

          {/* Bookings */}
          <div className="bg-white shadow-soft p-6">
            <div className="flex items-center mb-sm">
              <div className="w-12 h-12 bg-sage/10 flex items-center justify-center mr-4">
                <span className="text-2xl">📅</span>
              </div>
              <div>
                <CardTitle level={4}>Rezerwacje</CardTitle>
                <p className="text-sm text-charcoal-light">Zarządzaj bookingami</p>
              </div>
            </div>
            <p className="text-charcoal-light text-sm mb-sm">
              Przeglądaj i zarządzaj rezerwacjami
            </p>
            <button
              onClick={() => router.push('/admin/bookings')}
              className="inline-flex items-center px-container-sm py-2 bg-sage text-white text-sm hover:bg-sage/90 transition-colors"
            >
              Zobacz rezerwacje
              <span className="ml-2">→</span>
            </button>
          </div>

          {/* Newsletter */}
          <div className="bg-white shadow-soft p-6">
            <div className="flex items-center mb-sm">
              <div className="w-12 h-12 bg-lotus/10 flex items-center justify-center mr-4">
                <span className="text-2xl">📧</span>
              </div>
              <div>
                <CardTitle level={4}>Newsletter</CardTitle>
                <p className="text-sm text-charcoal-light">Zarządzaj subskrypcjami</p>
              </div>
            </div>
            <p className="text-charcoal-light text-sm mb-sm">
              Przeglądaj subskrybentów i wysyłaj kampanie
            </p>
            <button
              onClick={() => router.push('/admin/newsletter')}
              className="inline-flex items-center px-container-sm py-2 bg-lotus text-white text-sm hover:bg-lotus/90 transition-colors"
            >
              Zarządzaj newsletter
              <span className="ml-2">→</span>
            </button>
          </div>

          {/* Settings */}
          <div className="bg-white shadow-soft p-6">
            <div className="flex items-center mb-sm">
              <div className="w-12 h-12 bg-wood/10 flex items-center justify-center mr-4">
                <span className="text-2xl">⚙️</span>
              </div>
              <div>
                <CardTitle level={4}>Ustawienia</CardTitle>
                <p className="text-sm text-charcoal-light">Konfiguracja strony</p>
              </div>
            </div>
            <p className="text-charcoal-light text-sm mb-sm">
              Zarządzaj ustawieniami i konfiguracją
            </p>
            <button
              onClick={() => router.push('/admin/settings')}
              className="inline-flex items-center px-container-sm py-2 bg-wood text-white text-sm hover:bg-wood/90 transition-colors"
            >
              Ustawienia
              <span className="ml-2">→</span>
            </button>
          </div>

        </div>

        {/* Quick Stats */}
        <div className="mt-lg bg-white shadow-soft p-6">
          <CardTitle level={3} className="mb-sm">Szybkie statystyki</CardTitle>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-sm">
            <div className="text-center p-4 bg-charcoal/5">
              <div className="text-2xl font-bold text-charcoal">-</div>
              <div className="text-sm text-charcoal-light">Rezerwacje</div>
            </div>
            <div className="text-center p-4 bg-terra/5">
              <div className="text-2xl font-bold text-charcoal">-</div>
              <div className="text-sm text-charcoal-light">Newsletter</div>
            </div>
            <div className="text-center p-4 bg-sage/5">
              <div className="text-2xl font-bold text-charcoal">-</div>
              <div className="text-sm text-charcoal-light">Odwiedziny</div>
            </div>
            <div className="text-center p-4 bg-lotus/5">
              <div className="text-2xl font-bold text-charcoal">-</div>
              <div className="text-sm text-charcoal-light">Konwersje</div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
