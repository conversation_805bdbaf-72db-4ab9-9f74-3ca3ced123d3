'use client';

import { useState, useEffect } from 'react';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import {  motion, AnimatePresence  } from '@/components/ui/UnifiedButton';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';

export default function PWAInstaller() {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = window.navigator.standalone === true;
      setIsStandalone(isStandaloneMode || isIOSStandalone);
      setIsInstalled(isStandaloneMode || isIOSStandalone);
    };

    // Check if iOS
    const checkIfIOS = () => {
      const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
      setIsIOS(isIOSDevice);
    };

    // Register service worker
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          console.log('Service Worker registered:', registration);
          
          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content available, show update prompt
                showUpdatePrompt();
              }
            });
          });
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      }
    };

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setDeferredPrompt(e);
      
      // Show install prompt after user has been on site for a while
      setTimeout(() => {
        if (!isInstalled && !localStorage.getItem('pwa-install-dismissed')) {
          setShowInstallPrompt(true);
        }
      }, 30000); // Show after 30 seconds
    };

    checkIfInstalled();
    checkIfIOS();
    registerServiceWorker();

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt');
      setIsInstalled(true);
    } else {
      console.log('User dismissed the install prompt');
      localStorage.setItem('pwa-install-dismissed', 'true');
    }
    
    setDeferredPrompt(null);
    setShowInstallPrompt(false);
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    localStorage.setItem('pwa-install-dismissed', 'true');
  };

  const showUpdatePrompt = () => {
    if (confirm('Nowa wersja aplikacji jest dostępna. Czy chcesz ją załadować?')) {
      window.location.reload();
    }
  };

  // Don't show if already installed or on iOS (different install process)
  if (isInstalled || isStandalone) {
    return null;
  }

  // iOS install instructions
  if (isIOS && showInstallPrompt) {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 100 }}
          className="fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto"
        >
          <div className="bg-white rectangular shadow-2xl p-6 border border-enterprise-brown/10">
            <div className="flex items-start gap-sm">
              <div className="text-2xl /* TODO: Replace with SectionTitle */">📱</div>
              <div className="flex-1">
                <h3 className="font-medium text-enterprise-brown mb-2">
                  Zainstaluj aplikację
                </h3>
                <p className="text-sm text-charcoal-light mb-sm">
                  Dodaj Bali Yoga Journey do ekranu głównego:
                </p>
                <div className="space-y-2 text-xs text-charcoal-light">
                  <div className="flex items-center gap-2">
                    <span>1.</span>
                    <span>Naciśnij przycisk "Udostępnij" ⬆️</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>2.</span>
                    <span>Wybierz "Dodaj do ekranu głównego" ➕</span>
                  </div>
                </div>
              </div>
              <button
                onClick={handleDismiss}
                className="text-charcoal-light hover:text-enterprise-brown transition-colors"
              >
                ✕
              </button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }

  // Android/Desktop install prompt
  return (
    <AnimatePresence>
      {showInstallPrompt && deferredPrompt && (
        <motion.div
          initial={{ opacity: 0, y: 100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 100 }}
          className="fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto"
        >
          <div className="bg-gradient-to-r from-charcoal to-golden rectangular shadow-2xl p-6 text-white">
            <div className="flex items-start gap-sm">
              <div className="text-2xl">🧘‍♀️</div>
              <div className="flex-1">
                <h3 className="font-medium mb-2">
                  Zainstaluj aplikację Bali Yoga Journey
                </h3>
                <p className="text-sm opacity-90 mb-sm">
                  Szybki dostęp do retreatów, map i rezerwacji. Działa offline!
                </p>
                <div className="flex gap-3">
                  <button
                    onClick={handleInstallClick}
                    className="bg-white text-enterprise-brown px-container-sm py-2 rectangular text-sm font-medium hover:bg-white/90 transition-colors"
                  >
                    Zainstaluj
                  </button>
                  <button
                    onClick={handleDismiss}
                    className="text-white/80 hover:text-white transition-colors text-sm"
                  >
                    Nie teraz
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Hook for checking PWA install status
export function usePWAInstall() {
  const [isInstalled, setIsInstalled] = useState(false);
  const [canInstall, setCanInstall] = useState(false);

  useEffect(() => {
    const checkInstallStatus = () => {
      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = window.navigator.standalone === true;
      setIsInstalled(isStandaloneMode || isIOSStandalone);
    };

    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setCanInstall(true);
    };

    checkInstallStatus();
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  return { isInstalled, canInstall };
}

// Component for showing PWA features
export function PWAFeatures() {
  const { isInstalled } = usePWAInstall();

  if (!isInstalled) return null;

  return (
    <div className="bg-enterprise-brown/5 rectangular p-6 mb-lg">
      <h3 className="text-lg font-cormorant text-enterprise-brown mb-sm">
        🎉 Aplikacja zainstalowana!
      </h3>
      <div className="grid grid-cols-2 gap-sm text-sm">
        <div className="flex items-center gap-2">
          <span className="text-enterprise-brown">📱</span>
          <span>Szybki dostęp</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-enterprise-brown">⚡</span>
          <span>Działa offline</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-enterprise-brown">🔔</span>
          <span>Powiadomienia</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-enterprise-brown">💾</span>
          <span>Mniej danych</span>
        </div>
      </div>
    </div>
  );
}
