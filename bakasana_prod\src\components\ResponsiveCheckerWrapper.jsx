'use client'

import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'

const ResponsiveChecker = dynamic(() => import('@/components/ResponsiveChecker'), {
  loading: () => null,
  ssr: false
})

export default function ResponsiveCheckerWrapper() {
  const [isDevelopment, setIsDevelopment] = useState(false)

  useEffect(() => {
    // Sprawdź czy jesteśmy w development mode po stronie klienta
    setIsDevelopment(process.env.NODE_ENV === 'development')
  }, [])

  // Nie renderuj nic jeśli nie jesteśmy w development mode
  if (!isDevelopment) {
    return null
  }

  return <ResponsiveChecker />
}