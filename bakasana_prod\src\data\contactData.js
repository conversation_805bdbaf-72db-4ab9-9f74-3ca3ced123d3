import { Mail, Instagram, Facebook, CalendarCheck } from 'lucide-react';

export const contactInfo = {
  email: "<EMAIL>",
  phone: "+48 123 456 789", // Dodaj jeśli potrzebne
  address: "Rzeszów, Polska"
};

export const socialLinks = [
  {
    href: "mailto:<EMAIL>",
    label: "Email",
    icon: Mail,
    aria: "Email kontaktowy"
  },
  {
    href: "https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",
    label: "Instagram",
    icon: Instagram,
    aria: "Profil na Instagramie"
  },
  {
    href: "https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",
    label: "Facebook",
    icon: Facebook,
    aria: "Profil na Facebooku"
  },
  {
    href: "https://app.fitssey.com/Flywithbakasana/frontoffice",
    label: "<PERSON><PERSON><PERSON>",
    icon: CalendarCheck,
    aria: "<PERSON>il na <PERSON> (rezerwacje)"
  }
];

export const studioInfo = {
  name: "Studio Bakasana",
  description: "Regularne zajęcia jogi w Rzeszowie. Hatha, Vinyasa, Ashtanga Flow i więcej.",
  website: "https://flywithbakasana.pl/",
  location: "Rzeszów, Polska"
};
