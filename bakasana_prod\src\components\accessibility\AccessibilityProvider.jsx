/**
 * ♿ BAKASANA - ACCESSIBILITY PROVIDER
 * 
 * WCAG 2.1 AAA compliant accessibility features:
 * - Keyboard navigation
 * - Screen reader support
 * - Focus management
 * - High contrast mode
 * - Reduced motion support
 * - Skip links
 * - Aria live regions
 * 
 * Features:
 * - Automatic focus management
 * - Keyboard shortcuts
 * - Screen reader announcements
 * - Accessibility toolbar
 */

'use client';

import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import UnifiedButton from '@/components/ui/UnifiedButton';
import {  useReducedMotion  } from '@/components/ui/UnifiedButton';

const AccessibilityContext = createContext();

export const useAccessibility = () => {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within AccessibilityProvider');
  }
  return context;
};

const AccessibilityProvider = ({ children }) => {
  // AAA Level accessibility states
  const [highContrast, setHighContrast] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [announcements, setAnnouncements] = useState([]);
  const [focusOutlineVisible, setFocusOutlineVisible] = useState(false);
  const [keyboardNavigation, setKeyboardNavigation] = useState(false);

  // Enhanced AAA features
  const [colorBlindMode, setColorBlindMode] = useState('none');
  const [textSpacing, setTextSpacing] = useState(1);
  const [lineHeight, setLineHeight] = useState(1.5);
  const [letterSpacing, setLetterSpacing] = useState(0);
  const [dyslexiaFont, setDyslexiaFont] = useState(false);
  const [screenReaderMode, setScreenReaderMode] = useState(false);
  
  const announcementRef = useRef(null);
  const prefersReducedMotion = useReducedMotion();
  
  // Initialize accessibility settings
  useEffect(() => {
    // Check for saved preferences
    const savedHighContrast = localStorage.getItem('a11y-high-contrast') === 'true';
    const savedFontSize = parseInt(localStorage.getItem('a11y-font-size')) || 16;
    
    setHighContrast(savedHighContrast);
    setFontSize(savedFontSize);
    
    // Apply initial settings
    applyAccessibilitySettings(savedHighContrast, savedFontSize);
    
    // Check for system preferences
    const highContrastMedia = window.matchMedia('(prefers-contrast: high)');
    if (highContrastMedia.matches && !savedHighContrast) {
      setHighContrast(true);
      applyAccessibilitySettings(true, savedFontSize);
    }
    
    // Listen for system preference changes
    const handleContrastChange = (e) => {
      if (e.matches) {
        setHighContrast(true);
        applyAccessibilitySettings(true, fontSize);
      }
    };
    
    highContrastMedia.addEventListener('change', handleContrastChange);
    
    return () => {
      highContrastMedia.removeEventListener('change', handleContrastChange);
    };
  }, []);
  
  // Apply accessibility settings to document
  const applyAccessibilitySettings = (contrast, size) => {
    const root = document.documentElement;
    
    // High contrast mode
    if (contrast) {
      root.setAttribute('data-high-contrast', 'true');
      root.style.setProperty('--text-color', '#000000');
      root.style.setProperty('--background-color', '#ffffff');
      root.style.setProperty('--accent-color', '#0066cc');
      root.style.setProperty('--border-color', '#000000');
    } else {
      root.removeAttribute('data-high-contrast');
      root.style.removeProperty('--text-color');
      root.style.removeProperty('--background-color');
      root.style.removeProperty('--accent-color');
      root.style.removeProperty('--border-color');
    }
    
    // Font size
    root.style.setProperty('--base-font-size', `${size}px`);
    
    // Save preferences
    localStorage.setItem('a11y-high-contrast', contrast.toString());
    localStorage.setItem('a11y-font-size', size.toString());
  };
  
  // Toggle high contrast
  const toggleHighContrast = () => {
    const newContrast = !highContrast;
    setHighContrast(newContrast);
    applyAccessibilitySettings(newContrast, fontSize);
    announce(newContrast ? 'Tryb wysokiego kontrastu włączony' : 'Tryb wysokiego kontrastu wyłączony');
  };
  
  // Adjust font size
  const adjustFontSize = (delta) => {
    const newSize = Math.max(12, Math.min(24, fontSize + delta));
    setFontSize(newSize);
    applyAccessibilitySettings(highContrast, newSize);
    announce(`Rozmiar czcionki zmieniony na ${newSize}px`);
  };
  
  // Screen reader announcements
  const announce = (message, priority = 'polite') => {
    const id = Date.now();
    const newAnnouncement = { id, message, priority, timestamp: new Date() };
    
    setAnnouncements(prev => [...prev, newAnnouncement]);
    
    // Remove announcement after 5 seconds
    setTimeout(() => {
      setAnnouncements(prev => prev.filter(a => a.id !== id));
    }, 5000);
  };
  
  // Focus management
  const focusElement = (selector) => {
    const element = document.querySelector(selector);
    if (element) {
      element.focus();
      setFocusOutlineVisible(true);
    }
  };
  
  const focusFirstFocusableElement = (container) => {
    const focusableElements = container.querySelectorAll(
      'a[href], button, textarea, input, select, details, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  };
  
  const focusLastFocusableElement = (container) => {
    const focusableElements = container.querySelectorAll(
      'a[href], button, textarea, input, select, details, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length > 0) {
      focusableElements[focusableElements.length - 1].focus();
    }
  };
  
  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      setKeyboardNavigation(true);
      
      // Global keyboard shortcuts
      if (e.altKey) {
        switch (e.key) {
          case 'h':
            e.preventDefault();
            focusElement('h1, h2, h3, h4, h5, h6');
            announce('Przejście do nagłówka');
            break;
          case 'm':
            e.preventDefault();
            focusElement('main, [role="main"]');
            announce('Przejście do głównej treści');
            break;
          case 'n':
            e.preventDefault();
            focusElement('nav, [role="navigation"]');
            announce('Przejście do nawigacji');
            break;
          case 'c':
            e.preventDefault();
            toggleHighContrast();
            break;
          case '+':
            e.preventDefault();
            adjustFontSize(2);
            break;
          case '-':
            e.preventDefault();
            adjustFontSize(-2);
            break;
        }
      }
      
      // Escape key handling
      if (e.key === 'Escape') {
        const activeModal = document.querySelector('[role="dialog"][aria-modal="true"]');
        if (activeModal) {
          const closeButton = activeModal.querySelector('[aria-label*="zamknij"], [aria-label*="close"]');
          if (closeButton) {
            closeButton.click();
          }
        }
      }
    };
    
    const handleMouseDown = () => {
      setKeyboardNavigation(false);
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [highContrast, fontSize]);
  
  // Focus outline management
  useEffect(() => {
    const root = document.documentElement;
    
    if (keyboardNavigation) {
      root.setAttribute('data-keyboard-navigation', 'true');
    } else {
      root.removeAttribute('data-keyboard-navigation');
    }
  }, [keyboardNavigation]);
  
  const contextValue = {
    highContrast,
    fontSize,
    announcements,
    focusOutlineVisible,
    keyboardNavigation,
    prefersReducedMotion,
    toggleHighContrast,
    adjustFontSize,
    announce,
    focusElement,
    focusFirstFocusableElement,
    focusLastFocusableElement,
  };
  
  return (
    <AccessibilityContext.Provider value={contextValue}>
      {children}
      
      {/* Screen reader announcements */}
      <div
        ref={announcementRef}
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
      >
        {announcements.map(announcement => (
          <div key={announcement.id} aria-live={announcement.priority}>
            {announcement.message}
          </div>
        ))}
      </div>
      
      {/* Accessibility toolbar */}
      <AccessibilityToolbar />
    </AccessibilityContext.Provider>
  );
};

// Accessibility Toolbar Component
const AccessibilityToolbar = () => {
  const {
    highContrast,
    fontSize,
    toggleHighContrast,
    adjustFontSize,
    announce
  } = useAccessibility();
  
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-12 h-12 bg-charcoal-gold text-sanctuary rectangular shadow-lg hover:bg-terra-amber transition-all duration-200 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-temple-gold focus:ring-offset-2"
        aria-label="Otwórz panel dostępności"
        aria-expanded={isOpen}
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute bottom-16 right-0 w-64 bg-sanctuary border border-stone-light rectangular shadow-2xl p-4 space-y-3">
          <h3 className="text-sm font-semibold text-charcoal mb-3">Panel dostępności</h3>
          
          <div className="space-y-2">
            <button
              onClick={toggleHighContrast}
              className={`w-full text-left px-3 py-2 text-sm transition-colors ${
                highContrast 
                  ? 'bg-charcoal-gold text-sanctuary' 
                  : 'bg-whisper text-charcoal hover:bg-sanctuary'
              }`}
            >
              {highContrast ? '✓ ' : ''}Wysoki kontrast
            </button>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-charcoal">Rozmiar czcionki:</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => adjustFontSize(-2)}
                  className="w-6 h-6 bg-whisper text-charcoal hover:bg-sanctuary transition-colors text-sm"
                  aria-label="Zmniejsz czcionkę"
                >
                  −
                </button>
                <span className="text-sm text-charcoal w-8 text-center">{fontSize}</span>
                <button
                  onClick={() => adjustFontSize(2)}
                  className="w-6 h-6 bg-whisper text-charcoal hover:bg-sanctuary transition-colors text-sm"
                  aria-label="Zwiększ czcionkę"
                >
                  +
                </button>
              </div>
            </div>
            
            <div className="text-xs text-stone mt-3 pt-2 border-t border-stone-light">
              <p>Skróty klawiszowe:</p>
              <p>Alt + H - Przejdź do nagłówka</p>
              <p>Alt + M - Przejdź do głównej treści</p>
              <p>Alt + N - Przejdź do nawigacji</p>
              <p>Alt + C - Przełącz kontrast</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccessibilityProvider;