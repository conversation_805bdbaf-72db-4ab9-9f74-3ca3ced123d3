'use client';

import React, { memo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const OptimizedBreadcrumbs = memo(() => {
  const pathname = usePathname();
  
  // Don't show breadcrumbs on homepage
  if (pathname === '/') return null;
  
  const pathSegments = pathname.split('/').filter(Boolean);
  
  const breadcrumbItems = [
    { name: 'Strona główna', href: '/' },
    ...pathSegments.map((segment, index) => {
      const href = '/' + pathSegments.slice(0, index + 1).join('/');
      const name = getPageName(segment);
      return { name, href };
    })
  ];

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbItems.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": `https://bakasana-travel.blog${item.href}`
    }))
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <nav aria-label="Breadcrumb" className="bg-sanctuary py-4">
        <div className="container mx-auto px-container-sm">
          <ol className="flex items-center space-x-2 text-sm">
            {breadcrumbItems.map((item, index) => (
              <li key={item.href} className="flex items-center">
                {index > 0 && (
                  <span className="mx-2 text-sage">/</span>
                )}
                {index === breadcrumbItems.length - 1 ? (
                  <span className="text-charcoal font-medium" aria-current="page">
                    {item.name}
                  </span>
                ) : (
                  <Link 
                    href={item.href} 
                    className="text-sage hover:text-enterprise-brown transition-colors duration-200"
                  >
                    {item.name}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </div>
      </nav>
    </>
  );
});

function getPageName(segment) {
  const pageNames = {
    'program': 'Program',
    'retreaty': 'Retreaty',
    'galeria': 'Galeria',
    'kontakt': 'Kontakt',
    'blog': 'Blog',
    'o-mnie': 'O mnie',
    'zajecia-online': 'Zajęcia Online',
    'rezerwacja': 'Rezerwacja',
    'mapa': 'Mapa',
    'wellness': 'Wellness',
    'polityka-prywatnosci': 'Polityka Prywatności'
  };
  
  return pageNames[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
}

OptimizedBreadcrumbs.displayName = 'OptimizedBreadcrumbs';

export default OptimizedBreadcrumbs;