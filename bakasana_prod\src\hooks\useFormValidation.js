'use client';

import { useState, useCallback } from 'react';

// Common validation rules
export const validationRules = {
  required: (value, fieldName) => {
    if (!value || !value.toString().trim()) {
      return `${fieldName} jest wymagane`;
    }
    return '';
  },

  email: (value) => {
    if (!value) return '';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Podaj prawidłowy adres email';
    }
    return '';
  },

  minLength: (min) => (value, fieldName) => {
    if (!value) return '';
    if (value.toString().trim().length < min) {
      return `${fieldName} musi mieć co najmniej ${min} znaków`;
    }
    return '';
  },

  maxLength: (max) => (value, fieldName) => {
    if (!value) return '';
    if (value.toString().length > max) {
      return `${fieldName} nie może przekraczać ${max} znaków`;
    }
    return '';
  },

  pattern: (regex, message) => (value) => {
    if (!value) return '';
    if (!regex.test(value)) {
      return message;
    }
    return '';
  },

  phone: (value) => {
    if (!value) return '';
    const phoneRegex = /^(\+48\s?)?(\d{3}\s?\d{3}\s?\d{3}|\d{2}\s?\d{3}\s?\d{2}\s?\d{2})$/;
    if (!phoneRegex.test(value.replace(/\s/g, ''))) {
      return 'Podaj prawidłowy numer telefonu';
    }
    return '';
  },

  url: (value) => {
    if (!value) return '';
    try {
      new URL(value);
      return '';
    } catch {
      return 'Podaj prawidłowy adres URL';
    }
  },

  custom: (validatorFn) => validatorFn
};

// Predefined field configurations
export const fieldConfigs = {
  name: {
    rules: [
      validationRules.required,
      validationRules.minLength(2),
      validationRules.pattern(/^[a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ\s]+$/, 'Imię może zawierać tylko litery')
    ],
    fieldName: 'Imię'
  },

  email: {
    rules: [
      validationRules.required,
      validationRules.email
    ],
    fieldName: 'Email'
  },

  message: {
    rules: [
      validationRules.required,
      validationRules.minLength(10),
      validationRules.maxLength(1000)
    ],
    fieldName: 'Wiadomość'
  },

  phone: {
    rules: [
      validationRules.phone
    ],
    fieldName: 'Telefon'
  },

  subject: {
    rules: [
      validationRules.required,
      validationRules.minLength(3),
      validationRules.maxLength(100)
    ],
    fieldName: 'Temat'
  }
};

export const useFormValidation = (initialValues = {}, validationConfig = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validate a single field
  const validateField = useCallback((fieldName, value) => {
    const config = validationConfig[fieldName];
    if (!config) return '';

    const { rules, fieldName: displayName } = config;
    
    for (const rule of rules) {
      const error = rule(value, displayName);
      if (error) return error;
    }
    
    return '';
  }, [validationConfig]);

  // Validate all fields
  const validateForm = useCallback(() => {
    const newErrors = {};
    let isValid = true;

    Object.keys(validationConfig).forEach(fieldName => {
      const error = validateField(fieldName, values[fieldName] || '');
      if (error) {
        newErrors[fieldName] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [values, validateField, validationConfig]);

  // Handle field change
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    setValues(prev => ({ ...prev, [name]: value }));

    // Real-time validation if field was touched
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({ ...prev, [name]: error }));
    }
  }, [touched, validateField]);

  // Handle field blur
  const handleBlur = useCallback((e) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    
    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));
  }, [validateField]);

  // Handle form submission
  const handleSubmit = useCallback((onSubmit) => {
    return async (e) => {
      e.preventDefault();
      
      // Mark all fields as touched
      const allTouched = {};
      Object.keys(validationConfig).forEach(key => {
        allTouched[key] = true;
      });
      setTouched(allTouched);

      // Validate form
      const isValid = validateForm();
      
      if (!isValid) {
        return false;
      }

      setIsSubmitting(true);
      
      try {
        await onSubmit(values);
        return true;
      } catch (error) {
        console.error('Form submission error:', error);
        return false;
      } finally {
        setIsSubmitting(false);
      }
    };
  }, [values, validateForm, validationConfig]);

  // Reset form
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);

  // Set field value programmatically
  const setFieldValue = useCallback((fieldName, value) => {
    setValues(prev => ({ ...prev, [fieldName]: value }));
    
    // Validate if touched
    if (touched[fieldName]) {
      const error = validateField(fieldName, value);
      setErrors(prev => ({ ...prev, [fieldName]: error }));
    }
  }, [touched, validateField]);

  // Set field error programmatically
  const setFieldError = useCallback((fieldName, error) => {
    setErrors(prev => ({ ...prev, [fieldName]: error }));
  }, []);

  // Get field props for easy integration
  const getFieldProps = useCallback((fieldName) => ({
    name: fieldName,
    value: values[fieldName] || '',
    onChange: handleChange,
    onBlur: handleBlur,
    'aria-invalid': errors[fieldName] ? 'true' : 'false',
    'aria-describedby': errors[fieldName] ? `${fieldName}-error` : undefined
  }), [values, handleChange, handleBlur, errors]);

  // Check if form is valid
  const isValid = Object.keys(errors).length === 0 && 
                  Object.keys(validationConfig).every(key => touched[key]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    resetForm,
    setFieldValue,
    setFieldError,
    getFieldProps,
    validateField,
    validateForm
  };
};

export default useFormValidation;