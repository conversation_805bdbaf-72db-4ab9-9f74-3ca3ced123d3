import React from 'react';

import { HeroTitle, SectionTitle, CardTitle, BodyText } from '@/components/ui/UnifiedTypography';

const TrustBadges = () => {
  const badges = [
    {
      id: 'participants',
      value: '47+',
      label: 'Uczestnik<PERSON>',
      description: 'Zadowolonych klientów'
    },
    {
      id: 'rating',
      value: '4.9★',
      label: 'Średnia ocen',
      description: 'Na podstawie opinii'
    },
    {
      id: 'safety',
      value: '100%',
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Sprawdzone miejsca'
    },
    {
      id: 'experience',
      value: '5+',
      label: 'Lat doświadczenia',
      description: 'W prowadzeniu retreatów'
    }
  ];

  return (
    <section className="py-12 bg-gradient-to-r from-shell/30 to-bamboo/20">
      <div className="max-w-6xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding">
        <div className="text-center mb-lg">
          <SectionTitle level={3}>Dlaczego warto nam zaufać?</SectionTitle>
          <p className="text-charcoal-light/80 font-light">Sprawdzone doświadczenie i zadowoleni klienci</p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-md">
          {badges.map((badge, index) => (
            <div 
              key={badge.id}
              className="text-center p-6 bg-white/60 backdrop-blur-sm rectangular border border-enterprise-brown/10 hover:border-enterprise-brown/20 transition-all duration-300 hover:scale-105"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="text-3xl lg:text-4xl font-light text-enterprise-brown mb-2 font-cormorant /* TODO: Replace with HeroTitle */ /* TODO: Replace with SectionTitle */">
                {badge.value}
              </div>
              <div className="text-sm font-medium text-enterprise-brown mb-1">
                {badge.label}
              </div>
              <div className="text-xs text-charcoal-light/60">
                {badge.description}
              </div>
            </div>
          ))}
        </div>
        
        {/* Dodatkowe elementy zaufania */}
        <div className="mt-lg flex flex-wrap justify-center items-center gap-md text-sm text-charcoal-light/70">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rectangular"></div>
            <span>Certyfikowana instruktorka RYT 500</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rectangular"></div>
            <span>Magister fizjoterapii</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rectangular"></div>
            <span>Ubezpieczenie NNW</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rectangular"></div>
            <span>Małe grupy (max 12 osób)</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustBadges;
