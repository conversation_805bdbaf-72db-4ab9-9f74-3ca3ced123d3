'use client';

import React from 'react';
import { cn } from '@/lib/utils';

const ElegantQuote = ({
  quote,
  author,
  role,
  className,
  variant = 'default',
  size = 'default',
  ...props
}) => {
  const variants = {
    default: "bg-secondary/60 backdrop-blur-sm border border-accent/10",
    minimal: "bg-transparent border-l-2 border-accent/30 pl-8",
    elegant: "bg-gradient-elegant backdrop-blur-sm border border-accent/5",
    testimonial: "bg-secondary/40 backdrop-blur-sm border border-accent/10 shadow-soft",
  };

  const sizes = {
    sm: "p-6 text-base",
    default: "p-8 text-lg",
    lg: "p-10 text-xl",
    xl: "p-12 text-2xl",
  };

  return (
    <blockquote 
      className={cn(
        "relative rectangular transition-all duration-500 hover:shadow-medium hover:-translate-y-1",
        variants[variant],
        sizes[size],
        className
      )}
      data-aos="fade-up"
      data-aos-duration="800"
      {...props}
    >
      {/* Elegant quote mark */}
      <div className="absolute -top-4 -left-2 text-6xl text-accent/10 font-cormorant leading-none select-none /* TODO: Replace with HeroTitle */">
        "
      </div>

      {/* Quote content */}
      <div className="relative z-10">
        <p className="font-light italic leading-relaxed mb-md text-primary/85">
          "{quote}"
        </p>

        {/* Author info */}
        {(author || role) && (
          <footer className="flex items-center gap-sm pt-4 border-t border-accent/10">
            {/* Avatar placeholder */}
            <div className="w-12 h-12 rectangular bg-accent/10 flex items-center justify-center font-cormorant text-lg text-accent">
              {author?.charAt(0) || '?'}
            </div>
            
            <div>
              {author && (
                <cite className="block font-medium text-primary not-italic">
                  {author}
                </cite>
              )}
              {role && (
                <p className="text-sm text-primary/60 font-light">
                  {role}
                </p>
              )}
            </div>
          </footer>
        )}
      </div>

      {/* Decorative element */}
      <div className="absolute bottom-4 right-4 w-8 h-px bg-accent/20"></div>
    </blockquote>
  );
};

export default ElegantQuote;
