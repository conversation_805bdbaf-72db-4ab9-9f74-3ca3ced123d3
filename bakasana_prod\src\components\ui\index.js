/**
 * 🎨 BAKASANA - UNIFIED UI SYSTEM
 * Elegancja <PERSON> Money + Ciepły minimalizm + Organiczne elementy
 */

// === UNIFIED ICON SYSTEM ===
export { default as Icon, NavigationIcon, SocialIcon, ActionIcon, StatusIcon } from './IconSystem';

// === UNIFIED COMPONENTS - NEW DESIGN SYSTEM ===
export { default as UnifiedButton, CTAButton, SecondaryButton, GhostButton, LinkButton } from './UnifiedButton';
export { default as UnifiedCard, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, RetreatCard, TestimonialCard, ServiceCard, MinimalCard } from './UnifiedCard';
export { UnifiedInput, UnifiedTextarea, UnifiedLabel, UnifiedSelect, UnifiedCheckbox, InputError, InputHelper, FieldGroup } from './UnifiedInput';
export { HeroTitle, SectionTitle, CardTitle as TypographyCardTitle, SubTitle, BodyText, LeadText, SmallText, Quote, Badge, NavLink, FormLabel, StatNumber, StatLabel, Divider, OrganicAccent } from './UnifiedTypography';

// === BLOG COMPONENTS ===
export { AlertBox, InfoBox, WarningBox, SuccessBox, ErrorBox, HighlightBox, QuoteBox, StepBox, FeatureList, ProsAndCons, CTABox } from './BlogComponents';

// === LEGACY COMPONENTS ===
export { default as EnhancedButton } from './EnhancedButton';
export { default as GlassCard } from './GlassCard';
export { default as OptimizedLazyImage } from './OptimizedLazyImage';
export { default as ParallaxSection, MultiLayerParallax, ParallaxText } from './ParallaxSection';
export { default as SmoothScrollProvider, ScrollProgressIndicator, useScrollTrigger } from './SmoothScrollProvider';

// Re-export hooks
export { 
  useScrollReveal,
  useStaggeredReveal,
  useMagneticCursor,
  useParallax,
  useMouseFollow,
  useScrollProgress,
  useViewportSize,
  useSmoothScrollTo,
  useIntersectionObserver,
  useReducedMotion,
  useButtonInteractions
} from '../hooks/useAdvancedAnimations';

// Re-export accessibility
export { default as AccessibilityProvider, useAccessibility } from '../accessibility/AccessibilityProvider';

// Re-export utilities
export { cn, debounce, throttle, formatPrice, validateEmail, generateId, clamp, lerp } from '../../lib/utils';