import { Calendar, User, Tag, Palette, Sunrise, Sunset, Mountain, Droplet, Heart, MapPin, Sparkles } from 'lucide-react';

export const events = [
  {
    id: 1,
    title: 'Retreat Jogowy na Bali',
    description: '7-dniowy intensywny retreat jogowy w sercu Bali. Codzienne sesje jogi, medytacji i warsztaty.',
    date: '2024-06-15',
    location: 'Ubud, Bali',
    imageUrl: '/images/events/yoga-retreat.jpg',
    iconName: 'sunrise',
    tags: ['joga', 'medytacja', 'retreat', 'Bali'],
    price: '4500 PLN',
    spots: 12,
    duration: '7 dni',
    level: 'Wszystkie poziomy',
    instructor: '<PERSON>'
  },
  {
    id: 2,
    title: 'Warsztaty Medytacji',
    description: 'Weekendowe warsztaty medytacji mindfulness w otoczeniu natury.',
    date: '2024-07-20',
    location: 'Warszawa',
    imageUrl: '/images/events/meditation-workshop.jpg',
    iconName: 'heart',
    tags: ['medytacja', 'mindfulness', 'warsztaty'],
    price: '800 PLN',
    spots: 20,
    duration: '2 dni',
    level: 'Począ<PERSON>',
    instructor: '<PERSON>walska'
  }
];

export const getAllEvents = () => {
  return [...events].sort((a, b) => new Date(a.date) - new Date(b.date));
};

export const getEventById = (id) => {
  return events.find(event => event.id === id);
};

export const eventData = {
  highlights: [
    {
      title: "Gili Air",
      description: "Spokojne miejsce dla praktyki jogi",
      icon: Sunrise
    },
    {
      title: "Uluwatu",
      description: "Magiczne zachody słońca i joga",
      icon: Heart
    }
  ],
  program: {
    // Możesz dodać więcej szczegółów programu tutaj
    duration: "10 dni",
    maxParticipants: 10,
    locations: ["Gili Air", "Uluwatu", "Ubud"],
    included: [
      "Zajęcia jogi",
      "Zakwaterowanie",
      "Śniadania",
      "Transfery lokalne"
    ]
  }
};