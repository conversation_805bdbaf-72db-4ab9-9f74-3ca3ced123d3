import { useEffect } from 'react';
import { useReportWebVitals } from 'next/web-vitals';

import { onCLS, onFID, onLCP, onFCP, onTTFB } from 'web-vitals';

export function WebVitalsReporter() {
  useReportWebVitals((metric) => {

    
    // W produkcji wyślij do analytics
    if (process.env.NODE_ENV === 'production') {
      // Przykład wysyłania do Google Analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', metric.name, {
          custom_map: { metric_id: 'custom_metric' },
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          event_category: 'Web Vitals',
          event_label: metric.id,
          non_interaction: true,
        });
      }
    }
  });

  return null;
}

export default function WebVitals() {
  useEffect(() => {
    const sendMetric = ({ name, value, id }) => {

      
      if (window.gtag) {
        window.gtag('event', name, {
          value: Math.round(name === 'CLS' ? value * 1000 : value),
          event_category: 'Web Vitals',
          event_label: id,
          non_interaction: true,
        });
      }
    };

    onCLS(sendMetric);
    onFID(sendMetric);
    onLCP(sendMetric);
    onFCP(sendMetric);
    onTTFB(sendMetric);
  }, []);

  return null;
}