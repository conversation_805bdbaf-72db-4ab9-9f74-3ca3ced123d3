'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import UnifiedButton from '@/components/ui/UnifiedButton';

export default function Error({ error, reset }) {
  useEffect(() => {
    // Bezpieczne logowanie błędu do konsoli
    try {
      if (error) {
        console.warn("--- <PERSON><PERSON><PERSON>d Aplikacji Next.js ---");
        if (error.message) {
          console.warn("Wiadomość:", error.message);
        }
        if (error.stack) {
          console.warn("Stos wywołań:", error.stack);
        }
      }
    } catch (loggingError) {
      // Ignoruj błędy logowania
    }
  }, [error]);

  return (
    <main className="py-section-lg min-h-screen bg-gradient-to-b from-sanctuary via-sand-light/50 to-ocean/10 flex items-center justify-center">
      <div className="max-w-2xl mx-auto px-container-sm text-center">
        <h1 className="text-5xl md:text-6xl font-display text-charcoal tracking-tight mb-md">
          Ups!
        </h1>
        <h2 className="text-2xl md:text-3xl font-display text-charcoal mb-sm">
          Coś poszło nie tak
        </h2>
        <p className="text-lg text-charcoal-light/80 mb-lg">
          Przepraszamy, wystąpił nieoczekiwany błąd. Nasz zespół został powiadomiony.
        </p>
        <div className="flex flex-col sm:flex-row gap-sm justify-center">
          <button
            onClick={() => reset()}
            className="inline-flex items-center justify-center px-hero-padding py-3 bg-charcoal text-sanctuary rectangular hover:bg-charcoal/90 transition-colors elegant-border"
          >
            Spróbuj ponownie
          </button>
          <Link
            href="/"
            className="inline-flex items-center justify-center px-hero-padding py-3 bg-sanctuary text-charcoal border border-charcoal/20 rectangular hover:bg-sanctuary/80 transition-colors elegant-border elegant-border-hover"
          >
            Wróć na stronę główną
          </Link>
        </div>
      </div>
    </main>
  );
}