'use client';

import React, { useState, useEffect } from 'react';

const TestimonialSlider = ({ testimonials }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Show only the first testimonial or display multiple side by side
  const displayTestimonials = testimonials.slice(0, 3);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (displayTestimonials.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prevIndex) => 
          prevIndex === displayTestimonials.length - 1 ? 0 : prevIndex + 1
        );
      }, 6000);
      return () => clearInterval(interval);
    }
  }, [displayTestimonials.length]);

  if (!displayTestimonials.length) return null;

  return (
    <div className="import { UnifiedButton } from '@/components/ui/UnifiedButton';
max-w-6xl mx-auto">
      <div className="relative">
        {/* Desktop: Show all testimonials */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-xl">
          {displayTestimonials.map((testimonial, index) => (
            <div 
              key={index} 
              className={`text-center testimonial-card transition-all duration-700 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ 
                transitionDelay: `${index * 200}ms`,
                animationDelay: `${index * 200}ms`
              }}
            >
              <div className="relative p-8 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm hover:from-white/10 hover:to-white/15 transition-all duration-500">
                <div className="absolute inset-0 bg-gradient-to-r from-charcoal-gold/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
                
                <p className="text-sanctuary text-base leading-relaxed mb-md font-light relative z-10">
                  <span className="text-charcoal-gold/60 text-2xl font-cormorant leading-none /* TODO: Replace with SectionTitle */">"</span>
                  {testimonial.quote}
                  <span className="text-charcoal-gold/60 text-2xl font-cormorant leading-none">"</span>
                </p>

                <div className="space-y-2 relative z-10">
                  <h4 className="font-normal text-sanctuary text-sm tracking-wide">
                    {testimonial.author}
                  </h4>
                  <p className="text-sanctuary/70 text-xs font-light tracking-wider uppercase">
                    {testimonial.location}
                  </p>
                  {testimonial.retreat && (
                    <p className="text-charcoal-gold/80 text-xs font-light italic">
                      {testimonial.retreat}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile: Show sliding testimonials */}
        <div className="md:hidden">
          <div className="overflow-hidden relative">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {displayTestimonials.map((testimonial, index) => (
                <div 
                  key={index} 
                  className="w-full flex-shrink-0 px-container-sm"
                >
                  <div className="text-center testimonial-card">
                    <div className="relative p-8 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm">
                      <p className="text-sanctuary text-base leading-relaxed mb-md font-light">
                        <span className="text-charcoal-gold/60 text-2xl font-cormorant leading-none">"</span>
                        {testimonial.quote}
                        <span className="text-charcoal-gold/60 text-2xl font-cormorant leading-none">"</span>
                      </p>

                      <div className="space-y-2">
                        <h4 className="font-normal text-sanctuary text-sm tracking-wide">
                          {testimonial.author}
                        </h4>
                        <p className="text-sanctuary/70 text-xs font-light tracking-wider uppercase">
                          {testimonial.location}
                        </p>
                        {testimonial.retreat && (
                          <p className="text-charcoal-gold/80 text-xs font-light italic">
                            {testimonial.retreat}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Mobile indicators */}
          {displayTestimonials.length > 1 && (
            <div className="flex justify-center mt-lg space-x-2">
              {displayTestimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-2 h-2 transition-all duration-300 ${
                    index === currentIndex 
                      ? 'bg-charcoal-gold' 
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                  aria-label={`Przejdź do opinii ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TestimonialSlider;