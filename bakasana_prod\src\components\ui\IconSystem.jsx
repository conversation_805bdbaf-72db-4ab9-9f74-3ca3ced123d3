'use client';

/**
 * 🎨 BAKASANA - UNIFIED ICON SYSTEM
 * Centralized icon management with consistent styling
 * Using Lucide React for modern, consistent icons
 */

import { cn } from '@/lib/utils';
import {
  Menu, X, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, ArrowRight, ArrowLeft, ArrowUp, ArrowDown,
  Home, Search, Filter, Settings, MoreHorizontal, MoreVertical, Plus, Minus, Check, CheckCircle,
  Mail, Phone, Instagram, Facebook, MessageCircle, Send, Share2, ExternalLink,
  MapPin, Globe, Navigation, Compass, Map, Route, Plane, Car, Train, Ship,
  Calendar, CalendarDays, Clock, Timer, Hourglass, Sunrise, Sunset, Moon, Sun,
  User, Users, UserCheck, UserPlus, UserMinus, Crown, Award, Shield, Heart, Star,
  Image, Camera, Video, Play, Pause, Volume2, VolumeX, Download, Upload, File, FileText,
  CreditCard, DollarSign, ShoppingCart, Package, Truck, Store, Building, Briefcase,
  Mountain, Waves, Flower, Leaf, TreeDeciduous, Droplet, Wind, Zap, Sparkles, Target,
  Edit, Trash2, Copy, Save, RefreshCw, RotateCcw, Eye, EyeOff, Lock, Unlock,
  Info, AlertCircle, AlertTriangle, CheckCircle2, XCircle, HelpCircle, Bell, BellOff,
  Layout, Grid, List, Columns, Rows, Maximize, Minimize, Move, Palette,
  Wifi, WifiOff, Bluetooth, Battery, BatteryLow, Signal, SignalHigh, SignalLow, Smartphone, Laptop,
  Coffee, Utensils, Wine, Apple, Cake, Pizza, IceCream, ShoppingBag, Gift, BookOpen,
  Cloud, CloudRain, CloudSnow, CloudLightning, Thermometer, Umbrella, Rainbow, Snowflake
} from 'lucide-react';

/**
 * Icon size variants following BAKASANA design system
 */
const iconSizes = {
  xs: 'w-3 h-3',      // 12px
  sm: 'w-4 h-4',      // 16px
  md: 'w-5 h-5',      // 20px
  lg: 'w-6 h-6',      // 24px
  xl: 'w-8 h-8',      // 32px
  '2xl': 'w-10 h-10', // 40px
  '3xl': 'w-12 h-12', // 48px
};

/**
 * Icon color variants using BAKASANA color system
 */
const iconColors = {
  primary: 'text-charcoal',
  secondary: 'text-sage',
  accent: 'text-enterprise-brown',
  muted: 'text-stone',
  light: 'text-stone-light',
  white: 'text-sanctuary',
  success: 'text-green-600',
  warning: 'text-amber-600',
  error: 'text-red-600',
  info: 'text-blue-600',
};

/**
 * Unified Icon Component
 */
export function Icon({ 
  name, 
  size = 'md', 
  color = 'primary', 
  className = '', 
  strokeWidth = 1.5,
  ...props 
}) {
  const IconComponent = iconMap[name];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in IconSystem`);
    return null;
  }

  return (
    <IconComponent
      className={cn(
        iconSizes[size],
        iconColors[color],
        'transition-colors duration-200',
        className
      )}
      strokeWidth={strokeWidth}
      {...props}
    />
  );
}

/**
 * Icon mapping for easy access
 */
const iconMap = {
  // Navigation & UI
  'menu': Menu,
  'close': X,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  'arrow-right': ArrowRight,
  'arrow-left': ArrowLeft,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  'home': Home,
  'search': Search,
  'filter': Filter,
  'settings': Settings,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  'plus': Plus,
  'minus': Minus,
  'check': Check,
  'check-circle': CheckCircle,
  
  // Contact & Social
  'mail': Mail,
  'phone': Phone,
  'instagram': Instagram,
  'facebook': Facebook,
  'message': MessageCircle,
  'send': Send,
  'share': Share2,
  'external-link': ExternalLink,
  
  // Location & Travel
  'map-pin': MapPin,
  'globe': Globe,
  'navigation': Navigation,
  'compass': Compass,
  'map': Map,
  'route': Route,
  'plane': Plane,
  'car': Car,
  'train': Train,
  'ship': Ship,
  
  // Time & Calendar
  'calendar': Calendar,
  'calendar-days': CalendarDays,
  'clock': Clock,
  'timer': Timer,
  'hourglass': Hourglass,
  'sunrise': Sunrise,
  'sunset': Sunset,
  'moon': Moon,
  'sun': Sun,
  
  // User & People
  'user': User,
  'users': Users,
  'user-check': UserCheck,
  'user-plus': UserPlus,
  'user-minus': UserMinus,
  'crown': Crown,
  'award': Award,
  'shield': Shield,
  'heart': Heart,
  'star': Star,
  
  // Content & Media
  'image': Image,
  'camera': Camera,
  'video': Video,
  'play': Play,
  'pause': Pause,
  'volume': Volume2,
  'volume-off': VolumeX,
  'download': Download,
  'upload': Upload,
  'file': File,
  'file-text': FileText,
  
  // Business & Commerce
  'credit-card': CreditCard,
  'dollar': DollarSign,
  'shopping-cart': ShoppingCart,
  'package': Package,
  'truck': Truck,
  'store': Store,
  'building': Building,
  'briefcase': Briefcase,
  
  // Nature & Wellness
  'mountain': Mountain,
  'waves': Waves,
  'flower': Flower,
  'leaf': Leaf,
  'tree': TreeDeciduous,
  'droplet': Droplet,
  'wind': Wind,
  'zap': Zap,
  'sparkles': Sparkles,
  'target': Target,

  // Actions & States
  'edit': Edit,
  'trash': Trash2,
  'copy': Copy,
  'save': Save,
  'refresh': RefreshCw,
  'rotate': RotateCcw,
  'eye': Eye,
  'eye-off': EyeOff,
  'lock': Lock,
  'unlock': Unlock,
  
  // Alerts & Status
  'info': Info,
  'alert-circle': AlertCircle,
  'alert-triangle': AlertTriangle,
  'check-circle-2': CheckCircle2,
  'x-circle': XCircle,
  'help-circle': HelpCircle,
  'bell': Bell,
  'bell-off': BellOff,
  
  // Layout & Design
  'layout': Layout,
  'grid': Grid,
  'list': List,
  'columns': Columns,
  'rows': Rows,
  'maximize': Maximize,
  'minimize': Minimize,
  'move': Move,
  'palette': Palette,
  
  // Technology
  'wifi': Wifi,
  'wifi-off': WifiOff,
  'bluetooth': Bluetooth,
  'battery': Battery,
  'battery-low': BatteryLow,
  'signal': Signal,
  'signal-high': SignalHigh,
  'signal-low': SignalLow,
  'smartphone': Smartphone,
  'laptop': Laptop,
  
  // Food & Lifestyle
  'coffee': Coffee,
  'utensils': Utensils,
  'wine': Wine,
  'apple': Apple,
  'cake': Cake,
  'pizza': Pizza,
  'ice-cream': IceCream,
  'shopping-bag': ShoppingBag,
  'gift': Gift,
  'book': BookOpen,
  
  // Weather & Environment
  'cloud': Cloud,
  'cloud-rain': CloudRain,
  'cloud-snow': CloudSnow,
  'cloud-lightning': CloudLightning,
  'thermometer': Thermometer,
  'umbrella': Umbrella,
  'rainbow': Rainbow,
  'snowflake': Snowflake,
};

/**
 * Specialized Icon Components for common use cases
 */
export const NavigationIcon = ({ name, ...props }) => (
  <Icon name={name} size="md" color="primary" {...props} />
);

export const SocialIcon = ({ name, ...props }) => (
  <Icon name={name} size="lg" color="accent" {...props} />
);

export const ActionIcon = ({ name, ...props }) => (
  <Icon name={name} size="sm" color="secondary" {...props} />
);

export const StatusIcon = ({ name, ...props }) => (
  <Icon name={name} size="md" color="accent" {...props} />
);

// Export individual icons for backward compatibility
export {
  Menu, X, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, ArrowRight, ArrowLeft,
  Home, Search, Mail, Phone, Instagram, Facebook, MessageCircle, MapPin, Globe,
  Calendar, Clock, User, Users, Heart, Star, Mountain, Waves, Flower, Sparkles,
  Check, CheckCircle, Info, AlertCircle, Edit, Trash2, Settings, Plus, Minus
};

export default Icon;