/**
 * 🌊 BAKASANA - SMOOTH SCROLL PROVIDER
 *
 * Simplified version using native CSS scroll-behavior: smooth
 * Custom smooth scroll disabled to prevent mobile issues
 */

'use client';

import React, { useEffect, useRef, createContext, useContext } from 'react';
import { useReducedMotion } from '@/hooks/useAdvancedAnimations';

const SmoothScrollContext = createContext();

export const useSmoothScroll = () => {
  const context = useContext(SmoothScrollContext);
  if (!context) {
    throw new Error('useSmoothScroll must be used within a SmoothScrollProvider');
  }
  return context;
};

const SmoothScrollProvider = ({ children }) => {
  const scrollRef = useRef(null);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    if (prefersReducedMotion) return;

    // Use native CSS scroll-behavior: smooth instead of custom implementation
    // This prevents mobile scrolling issues
    return () => {};
  }, [prefersReducedMotion]);
  
  // Scroll to element function
  const scrollToElement = (selector, options = {}) => {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const defaultOptions = {
      duration: 1000,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      offset: 0,
      ...options
    };
    
    const targetPosition = element.offsetTop + defaultOptions.offset;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    const startTime = performance.now();
    
    const easeOutCubic = (t) => {
      return 1 - Math.pow(1 - t, 3);
    };
    
    const animateScroll = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / defaultOptions.duration, 1);
      const easeProgress = easeOutCubic(progress);
      
      window.scrollTo(0, startPosition + distance * easeProgress);
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };
    
    requestAnimationFrame(animateScroll);
  };
  
  // Scroll to top function
  const scrollToTop = (options = {}) => {
    scrollToElement('body', { duration: 800, ...options });
  };
  
  // Get scroll progress
  const getScrollProgress = () => {
    const scrollTop = window.pageYOffset;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    return (scrollTop / docHeight) * 100;
  };
  
  const contextValue = {
    scrollToElement,
    scrollToTop,
    getScrollProgress
  };
  
  return (
    <SmoothScrollContext.Provider value={contextValue}>
      {children}
    </SmoothScrollContext.Provider>
  );
};

export default SmoothScrollProvider;

// Scroll Progress Indicator Component
export const ScrollProgressIndicator = ({ 
  className = '',
  color = 'var(--temple-gold)',
  height = '3px',
  position = 'fixed',
  zIndex = 1000
}) => {
  const [progress, setProgress] = React.useState(0);
  const { getScrollProgress } = useSmoothScroll();
  
  React.useEffect(() => {
    const updateProgress = () => {
      setProgress(getScrollProgress());
    };
    
    updateProgress();
    window.addEventListener('scroll', updateProgress, { passive: true });
    
    return () => window.removeEventListener('scroll', updateProgress);
  }, [getScrollProgress]);
  
  return (
    <div
      className={`w-full transition-all duration-300 ${className}`}
      style={{
        position,
        top: 0,
        left: 0,
        height,
        background: 'rgba(42, 39, 36, 0.1)',
        zIndex
      }}
    >
      <div
        className="h-full transition-all duration-300 ease-out"
        style={{
          width: `${progress}%`,
          background: color,
          boxShadow: `0 0 10px ${color}40`
        }}
      />
    </div>
  );
};

// Scroll Trigger Hook
export const useScrollTrigger = (selector, options = {}) => {
  const [isTriggered, setIsTriggered] = React.useState(false);
  
  React.useEffect(() => {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const defaultOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -10% 0px',
      ...options
    };
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsTriggered(entry.isIntersecting);
      },
      defaultOptions
    );
    
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [selector, options]);
  
  return isTriggered;
};