import Link from 'next/link'
import Image from 'next/image'
import { Icon } from '@/components/ui/IconSystem';
import { HeroTitle, SectionTitle, BodyText, CardTitle, LeadText } from '@/components/ui/UnifiedTypography';
import { sriLankaProgramData, destinations } from '@/data/programData'
import { Calendar, ChevronLeft, MapPin, Users } from 'lucide-react';

export const metadata = {
  title: 'Retreaty Jogi Sri Lanka - Ayurveda i Buddyzm | BAKASANA',
  description: 'Odk<PERSON>j starożytną mądrość Sri Lanki na retreatach jogi z Julią Jakubowicz. Sigiriya, Kandy, Ella - połączenie jogi, ayurvedy i buddyjskich praktyk w perle Oceanu Indyjskiego.',
  keywords: 'retreat jogi sri lanka, ayurveda sri lanka, sigiriya joga, kandy retreat, ella joga, buddyzm sri lanka, julia j<PERSON><PERSON> sri lanka',
  openGraph: {
    title: 'Retreaty Jogi Sri Lanka - BAKASANA',
    description: 'Transformacyjne retreaty jogi w perle Oceanu Indyjskiego. Odkryj mądro<PERSON> ayurvedy i buddyzmu.',
    images: ['/og-image.jpg'], // Using existing OG image until Sri Lanka hero is added
  }
}

export default function SriLankaRetreatPage() {
  const destination = destinations.srilanka
  
  const highlights = [
    {
      title: 'Sigiriya',
      description: 'Forteca w niebie',
      icon: '—'  // Simple line drawing
    },
    {
      title: 'Ajurweda',
      description: 'Healing wisdom',
      icon: '—'  // Single leaf outline
    },
    {
      title: 'Buddyzm',
      description: 'Sacred silence',
      icon: 'ॐ'  // Om symbol
    },
    {
      title: 'Herbata',
      description: 'Górskie plantacje',
      icon: '—'
    },
    {
      title: 'Ella',
      description: 'Górski spokój',
      icon: '—'
    },
    {
      title: 'Galle',
      description: 'Ocean heritage',
      icon: '—'
    }
  ]

  const upcomingRetreats = [
    {
      id: 'sri-lanka-march-2024',
      title: 'Wiosenna Regeneracja',
      dates: '15-25 marca 2024',
      price: '€2,800',
      participants: '6/12',
      status: 'Dostępne',
      description: 'Pierwsze w roku zanurzenie w kulturę Sri Lanki'
    },
    {
      id: 'sri-lanka-june-2024',
      title: 'Letnia Transformacja',
      dates: '10-20 czerwca 2024',
      price: '€2,800',
      participants: '9/12',
      status: 'Wypełnia się',
      description: 'Intensywny retreat w porze suchej'
    },
    {
      id: 'sri-lanka-september-2024',
      title: 'Jesienne Odkrywanie',
      dates: '5-15 września 2024',
      price: '€2,800',
      participants: '3/12',
      status: 'Ostatnie miejsca',
      description: 'Idealna pora na eksplorację wyspy'
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-[70vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="/images/destinations/sri-lanka-hero.webp"
            alt="Sri Lanka - Perła Oceanu Indyjskiego"
            fill
            priority
            className="object-cover"
            quality={95}
          />
          <div className="absolute inset-0 bg-black/30"></div>
        </div>
        
        <div className="relative z-10 text-center text-white px-hero-padding max-w-4xl mx-auto">
          <div className="mb-md">
            <span className="inline-block px-container-sm py-2 border border-white/50 text-sm font-light tracking-wider uppercase">
              Sri Lanka Retreats
            </span>
          </div>
          <h1 className="text-5xl md:text-6xl font-cormorant font-light mb-md tracking-tight /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */" /* TODO: Replace with HeroTitle */>
            Sri Lanka
          </h1>
          <LeadText>
            Ajurweda • Buddyzm • Transformacja
          </LeadText>
        </div>
      </section>

      {/* Navigation */}
      <nav className="container py-6">
        <Link 
          href="/" 
          className="inline-flex items-center gap-2 text-gray-600 hover:text-black transition-colors"
        >
          <ChevronLeft size={16} />
          Powrót do strony głównej
        </Link>
      </nav>

      {/* Destination Overview */}
      <section className="container mb-2xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-xl items-center">
          <div className="space-y-md">
            <h2 className="text-4xl font-cormorant font-light text-black /* TODO: Replace with HeroTitle */" /* TODO: Replace with SectionTitle */>
              Dlaczego Sri Lanka?
            </h2>
            <div className="space-y-sm text-gray-600 font-light leading-relaxed">
              <p>
                Wyspa, gdzie czas płynie inaczej
              </p>
              <p>
                Każdy zachód słońca to nowe narodziny
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-sm pt-6">
              <div className="text-center">
                <div className="text-2xl font-cormorant font-light text-black">10 dni</div>
                <div className="text-sm text-gray-500 uppercase tracking-wide">Czas retreatu</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500 uppercase tracking-wide">Szczegóły przy kontakcie</div>
              </div>
            </div>
          </div>

          <div className="aspect-[4/5] relative overflow-hidden">
            <Image
              src="/images/destinations/sri-lanka-culture.webp"
              alt="Kultura Sri Lanki"
              fill
              className="object-cover"
              quality={95}
            />
          </div>
        </div>
      </section>

      {/* Highlights Grid */}
      <section className="container mb-2xl">
        <div className="text-center mb-xl">
          <h2 className="text-4xl font-cormorant font-light mb-md text-black" /* TODO: Replace with SectionTitle */>
            Doświadczenia Sri Lanki
          </h2>
          <p className="text-lg text-gray-600 font-light max-w-3xl mx-auto">
            Każdy element naszego programu został starannie zaplanowany, aby zapewnić 
            autentyczne połączenie z duchem i kulturą tej wyjątkowej wyspy.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-lg">
          {highlights.map((highlight, index) => (
            <div key={index} className="text-center space-y-sm p-6">
              <div className="text-4xl mb-sm">{highlight.icon}</div>
              <CardTitle className="text-black">
                {highlight.title}
              </CardTitle>
              <p className="text-gray-600 font-light leading-relaxed">
                {highlight.description}
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Program Preview */}
      <section className="container mb-2xl">
        <div className="text-center mb-xl">
          <h2 className="text-4xl font-cormorant font-light mb-md text-black" /* TODO: Replace with SectionTitle */>
            Program Retreatu
          </h2>
          <p className="text-lg text-gray-600 font-light max-w-3xl mx-auto mb-lg">
            Dziesięć dni pełnych odkryć, praktyki i głębokiej transformacji.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-lg">
          {sriLankaProgramData.slice(0, 6).map((day) => (
            <div key={day.day} className="space-y-sm">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src={day.image || '/images/placeholder/sri-lanka-day.webp'}
                  alt={day.title}
                  fill
                  className="object-cover"
                  quality={90}
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-light text-gray-500 uppercase tracking-wide">
                    Dzień {day.day}
                  </span>
                </div>
                <CardTitle className="text-black">
                  {day.title}
                </CardTitle>
                <p className="text-gray-600 font-light text-sm">
                  {day.activities.slice(0, 3).join(' • ')}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-xl">
          <Link 
            href="/program"
            className="inline-block px-hero-padding py-4 border border-black text-black font-light tracking-wide uppercase text-sm hover:opacity-70 transition-opacity"
          >
            Zobacz pełny program
          </Link>
        </div>
      </section>

      {/* Upcoming Retreats */}
      <section className="bg-gray-50 py-section-md">
        <div className="container">
          <div className="text-center mb-xl">
            <h2 className="text-4xl font-cormorant font-light mb-md text-black" /* TODO: Replace with SectionTitle */>
              Nadchodzące Retreaty
            </h2>
            <p className="text-lg text-gray-600 font-light max-w-3xl mx-auto">
              Dołącz do nas na transformacyjnej podróży do perły Oceanu Indyjskiego.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-lg">
            {upcomingRetreats.map((retreat) => (
              <div key={retreat.id} className="bg-white p-8 space-y-md">
                <div className="space-y-sm">
                  <div className="flex items-center justify-between">
                    <h3 className="text-2xl font-cormorant font-light text-black" /* TODO: Replace with CardTitle */>
                      {retreat.title}
                    </h3>
                    <span className={`text-xs px-3 py-1 uppercase tracking-wide font-light ${
                      retreat.status === 'Dostępne' ? 'bg-green-100 text-green-800' :
                      retreat.status === 'Wypełnia się' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {retreat.status}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-gray-600 font-light">
                    <div className="flex items-center gap-2">
                      <Calendar size={16} />
                      <span>{retreat.dates}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users size={16} />
                      <span>{retreat.participants} uczestniczek</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin size={16} />
                      <span>Sri Lanka</span>
                    </div>
                  </div>

                  <p className="text-gray-600 font-light leading-relaxed">
                    {retreat.description}
                  </p>

                  <div className="flex items-center justify-between pt-4">
                    <div className="text-2xl font-cormorant font-light text-black">
                      {retreat.price}
                    </div>
                    <Link 
                      href="/kontakt"
                      className="px-hero-padding py-3 border border-black text-black font-light tracking-wide uppercase text-sm hover:opacity-70 transition-opacity"
                    >
                      Rezerwuj
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-section-md text-center">
        <div className="max-w-3xl mx-auto space-y-lg">
          <h2 className="text-4xl font-cormorant font-light text-black" /* TODO: Replace with SectionTitle */>
            Gotowa na transformację?
          </h2>
          <p className="text-lg text-gray-600 font-light leading-relaxed">
            Sri Lanka czeka, aby podzielić się swoją mądrością. Dołącz do nas na tej 
            niezapomnianej podróży łączącej praktykę jogi z odkrywaniem perły Oceanu Indyjskiego.
          </p>
          <div className="flex flex-col sm:flex-row gap-sm justify-center">
            <Link 
              href="/kontakt"
              className="px-hero-padding py-4 bg-black text-white font-light tracking-wide uppercase text-sm hover:opacity-90 transition-opacity"
            >
              Skontaktuj się z nami
            </Link>
            <Link 
              href="/program"
              className="px-hero-padding py-4 border border-black text-black font-light tracking-wide uppercase text-sm hover:opacity-70 transition-opacity"
            >
              Zobacz program Bali
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}