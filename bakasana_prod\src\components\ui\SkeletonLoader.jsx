'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * 💀 SkeletonLoader - Advanced Loading States for Perfect UX
 * 
 * Features:
 * - Responsive skeleton screens
 * - Smooth animations
 * - Accessibility compliant
 * - Performance optimized
 * - Multiple variants
 */

const skeletonVariants = {
  // Basic shapes with premium shimmer effect
  text: 'h-4 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',
  heading: 'h-8 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',
  title: 'h-12 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',
  hero: 'h-16 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',

  // Interactive elements
  button: 'h-12 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',
  input: 'h-12 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',

  // Media with enhanced shimmer
  image: 'aspect-video bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',
  avatar: 'w-12 h-12 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer rounded-full',

  // Layout
  card: 'h-64 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer',
  section: 'h-96 bg-gradient-to-r from-sand-light/20 via-sand-light/50 to-sand-light/20 bg-[length:200%_100%] animate-shimmer'
};

const sizeVariants = {
  xs: 'h-3',
  sm: 'h-4',
  md: 'h-6',
  lg: 'h-8',
  xl: 'h-12',
  '2xl': 'h-16'
};

export default function SkeletonLoader({
  variant = 'text',
  size,
  width = 'w-full',
  className = '',
  animate = true,
  ...props
}) {
  const baseClasses = cn(
    'animate-pulse',
    skeletonVariants[variant],
    size && sizeVariants[size],
    width,
    animate && 'animate-shimmer',
    className
  );

  return (
    <div
      className={baseClasses}
      role="status"
      aria-label="Loading content"
      {...props}
    />
  );
}

// Specialized skeleton components

export function TextSkeleton({ lines = 3, className = '', ...props }) {
  return (
    <div className={cn('space-y-2', className)} {...props}>
      {Array.from({ length: lines }).map((_, index) => (
        <SkeletonLoader
          key={index}
          variant="text"
          width={index === lines - 1 ? 'w-3/4' : 'w-full'}
        />
      ))}
    </div>
  );
}

export function CardSkeleton({ className = '', ...props }) {
  return (
    <div className={cn('p-6 space-y-4', className)} {...props}>
      <SkeletonLoader variant="image" className="mb-4" />
      <SkeletonLoader variant="heading" width="w-3/4" />
      <TextSkeleton lines={3} />
      <SkeletonLoader variant="button" width="w-1/2" />
    </div>
  );
}

export function ServiceCardSkeleton({ className = '', ...props }) {
  return (
    <div className={cn('p-8 space-y-6 bg-sanctuary', className)} {...props}>
      <SkeletonLoader variant="heading" width="w-2/3" className="mx-auto" />
      <TextSkeleton lines={4} />
      <SkeletonLoader variant="button" width="w-1/3" className="mx-auto" />
    </div>
  );
}

export function TestimonialSkeleton({ className = '', ...props }) {
  return (
    <div className={cn('p-6 space-y-4 bg-sanctuary', className)} {...props}>
      <div className="flex space-x-2 mb-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <SkeletonLoader key={index} variant="text" width="w-4" size="xs" />
        ))}
      </div>
      <TextSkeleton lines={3} />
      <div className="flex items-center space-x-3 mt-4">
        <SkeletonLoader variant="avatar" className="rounded-full" />
        <div className="space-y-2">
          <SkeletonLoader variant="text" width="w-24" />
          <SkeletonLoader variant="text" width="w-32" size="xs" />
        </div>
      </div>
    </div>
  );
}

export function HeroSkeleton({ className = '', ...props }) {
  return (
    <div className={cn('text-center space-y-8 py-24', className)} {...props}>
      <SkeletonLoader variant="text" width="w-48" className="mx-auto" size="sm" />
      <SkeletonLoader variant="hero" width="w-3/4" className="mx-auto" />
      <SkeletonLoader variant="heading" width="w-1/2" className="mx-auto" />
      <TextSkeleton lines={2} className="max-w-2xl mx-auto" />
      
      {/* Stats skeleton */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="text-center space-y-2">
            <SkeletonLoader variant="heading" width="w-16" className="mx-auto" />
            <SkeletonLoader variant="text" width="w-24" className="mx-auto" size="sm" />
          </div>
        ))}
      </div>
      
      {/* Buttons skeleton */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <SkeletonLoader variant="button" width="w-48" />
        <SkeletonLoader variant="button" width="w-48" />
      </div>
    </div>
  );
}

export function NavigationSkeleton({ className = '', ...props }) {
  return (
    <div className={cn('flex items-center justify-between p-4', className)} {...props}>
      <SkeletonLoader variant="text" width="w-32" size="lg" />
      <div className="hidden lg:flex space-x-8">
        {Array.from({ length: 5 }).map((_, index) => (
          <SkeletonLoader key={index} variant="text" width="w-20" />
        ))}
      </div>
      <SkeletonLoader variant="button" width="w-12" className="lg:hidden" />
    </div>
  );
}

export function FooterSkeleton({ className = '', ...props }) {
  return (
    <div className={cn('bg-whisper p-8 space-y-8', className)} {...props}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="space-y-4">
            <SkeletonLoader variant="heading" width="w-32" size="md" />
            <div className="space-y-2">
              {Array.from({ length: 4 }).map((_, linkIndex) => (
                <SkeletonLoader key={linkIndex} variant="text" width="w-24" />
              ))}
            </div>
          </div>
        ))}
      </div>
      <div className="border-t border-stone/20 pt-8 text-center">
        <SkeletonLoader variant="text" width="w-64" className="mx-auto" />
      </div>
    </div>
  );
}

// Page-level skeleton layouts

export function HomePageSkeleton() {
  return (
    <div className="min-h-screen">
      <NavigationSkeleton />
      <HeroSkeleton />
      
      {/* About section skeleton */}
      <section className="py-24 bg-linen">
        <div className="container mx-auto px-8 text-center space-y-8">
          <SkeletonLoader variant="title" width="w-2/3" className="mx-auto" />
          <TextSkeleton lines={3} className="max-w-4xl mx-auto" />
        </div>
      </section>
      
      {/* Services section skeleton */}
      <section className="py-24 bg-sanctuary">
        <div className="container mx-auto px-8">
          <div className="text-center mb-16">
            <SkeletonLoader variant="title" width="w-1/2" className="mx-auto mb-8" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <ServiceCardSkeleton key={index} />
            ))}
          </div>
        </div>
      </section>
      
      {/* Testimonials section skeleton */}
      <section className="py-24 bg-whisper">
        <div className="container mx-auto px-8">
          <div className="text-center mb-16">
            <SkeletonLoader variant="title" width="w-1/2" className="mx-auto mb-8" />
            <TextSkeleton lines={2} className="max-w-2xl mx-auto" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <TestimonialSkeleton key={index} />
            ))}
          </div>
        </div>
      </section>
      
      <FooterSkeleton />
    </div>
  );
}

// Loading state hook
export function useLoadingState(initialState = true) {
  const [isLoading, setIsLoading] = React.useState(initialState);
  
  const startLoading = React.useCallback(() => setIsLoading(true), []);
  const stopLoading = React.useCallback(() => setIsLoading(false), []);
  
  return { isLoading, startLoading, stopLoading };
}

// Progressive loading component
export function ProgressiveLoader({ 
  children, 
  skeleton, 
  isLoading = true, 
  delay = 0,
  className = '' 
}) {
  const [showContent, setShowContent] = React.useState(!isLoading);
  
  React.useEffect(() => {
    if (!isLoading) {
      const timer = setTimeout(() => setShowContent(true), delay);
      return () => clearTimeout(timer);
    } else {
      setShowContent(false);
    }
  }, [isLoading, delay]);
  
  return (
    <div className={className}>
      {showContent ? children : skeleton}
    </div>
  );
}
